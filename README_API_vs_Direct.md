# RAGFlow PDF Processing: API vs Direct Method

This document explains two approaches to process PDF files with RAGFlow and make them visible in the RAGFlow UI.

## 🎯 Quick Answer to Your Questions

### 1. Making Direct Method Results Visible in RAGFlow UI

**Current Status**: Your `main_direct.py` processes PDFs but results are only stored in Elasticsearch, not registered in RAGFlow's database.

**Solution**: The direct method needs additional integration with RAGFlow's database to be visible in the UI. This requires:
- Creating knowledge bases in RAGFlow's database
- Registering documents in RAGFlow's document service
- Proper indexing with RAGFlow's naming conventions

### 2. Complete API-Based Processing

**New Solution**: `main_api.py` - Uses RAGFlow's REST API for complete processing pipeline.

## 📁 Files Overview

- `main_direct.py` - Direct method using RAGFlow's internal functions
- `main_api.py` - **NEW** API-based method using RAGFlow's REST API
- `get_api_key.py` - Helper to obtain your RAGFlow API key
- `README_API_vs_Direct.md` - This documentation

## 🚀 Method 1: API-Based Processing (Recommended for UI Visibility)

### Prerequisites

1. **Get your RAGFlow API Key**:
   ```bash
   # Option 1: Use helper script
   python get_api_key.py
   
   # Option 2: Manual method
   # - Open http://eoaagmld007.int.cgg.com in browser
   # - Login to RAGFlow
   # - Go to Settings -> API Keys
   # - Create/copy your API key
   ```

2. **Set API Key**:
   ```bash
   export RAGFLOW_API_KEY='your-actual-api-key-here'
   ```

### Usage

```bash
# Process default PDF (g2mod.pdf)
python main_api.py

# Process custom PDF
python main_api.py /path/to/your.pdf /output/directory your-api-key
```

### What It Does

1. **Creates Dataset** - Creates a new dataset in RAGFlow
2. **Uploads PDF** - Uploads your PDF to the dataset
3. **Parses Document** - Initiates RAGFlow's parsing pipeline
4. **Waits for Completion** - Monitors parsing progress
5. **Retrieves Chunks** - Gets processed chunks
6. **Saves All Outputs** - Saves each step's results locally

### Output Structure

```
/ml/shared/lazhang/data/
└── g2mod_api_20250624_123456/
    ├── 01_dataset_creation.json
    ├── 02_document_upload.json
    ├── 03_parsing_initiation.json
    ├── 04_parsing_completion.json
    ├── 05_chunks_retrieval.json
    ├── 05_readable_chunks.json
    └── 06_processing_summary.json
```

### ✅ Benefits of API Method

- **Visible in RAGFlow UI** - Documents appear in RAGFlow interface
- **Searchable** - Can search and retrieve chunks via UI
- **Stable** - Uses official API, less likely to break
- **Remote Processing** - Can process on RAGFlow server
- **Step-by-step Outputs** - Still saves intermediate results

## 🔧 Method 2: Direct Processing (Current Method)

### Usage

```bash
# Your existing method
python main_direct.py
```

### ✅ Benefits of Direct Method

- **Full Control** - Access to all internal functions
- **Detailed Outputs** - More granular step outputs
- **No Network Dependency** - Runs locally
- **Custom Configurations** - Fine-tune every parameter

### ❌ Limitations

- **Not Visible in UI** - Results stored only in Elasticsearch
- **Complex Setup** - Requires RAGFlow internal dependencies
- **Potential Compatibility Issues** - May break with RAGFlow updates

## 🎯 Recommendation

**For your use case, use the API method (`main_api.py`)** because:

1. ✅ **Documents will be visible and searchable in RAGFlow UI**
2. ✅ **Still provides step-by-step outputs for analysis**
3. ✅ **Simpler setup and more stable**
4. ✅ **Official supported method**

## 📊 Comparison Table

| Feature | Direct Method | API Method |
|---------|---------------|------------|
| UI Visibility | ❌ No | ✅ Yes |
| Step Outputs | ✅ Detailed | ✅ Available |
| Setup Complexity | ❌ High | ✅ Simple |
| Stability | ⚠️ May break | ✅ Stable |
| Customization | ✅ Full | ⚠️ Limited |
| Network Required | ✅ No | ❌ Yes |

## 🚀 Getting Started with API Method

1. **Get API Key**:
   ```bash
   python get_api_key.py
   ```

2. **Set Environment Variable**:
   ```bash
   export RAGFLOW_API_KEY='your-key-here'
   ```

3. **Run Processing**:
   ```bash
   python main_api.py
   ```

4. **Check Results**:
   - Local outputs: `/ml/shared/lazhang/data/g2mod_api_*/`
   - RAGFlow UI: `http://eoaagmld007.int.cgg.com`

## 🔍 Troubleshooting

### API Key Issues
```bash
# Check if API key is set
echo $RAGFLOW_API_KEY

# Test API connection
curl -H "Authorization: Bearer $RAGFLOW_API_KEY" \
     http://eoaagmld007.int.cgg.com/api/v1/datasets
```

### Parsing Takes Too Long
- Default timeout: 300 seconds (5 minutes)
- Large PDFs may take longer
- Check RAGFlow UI for progress

### No Chunks Retrieved
- Parsing may have failed
- Check document status in RAGFlow UI
- Review parsing completion output

## 📝 Next Steps

1. **Try API method first** - It's the recommended approach
2. **Compare outputs** - See how API results differ from direct method
3. **Integrate with your workflow** - Adapt the API method to your needs
4. **Consider hybrid approach** - Use both methods for comparison

The API method will give you exactly what you want: **processed documents visible in RAGFlow UI with step-by-step outputs saved locally for analysis**.
