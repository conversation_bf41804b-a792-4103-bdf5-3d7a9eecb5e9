#!/usr/bin/env python3
"""
RAGFlow API-based Document Processing Pipeline
Processes PDF files using RAGFlow's REST API:
1. Create dataset via API
2. Upload PDF documents via API
3. Parse documents via API
4. Retrieve chunks via API
5. Save all step outputs for analysis
"""

import os
import sys
import json
import time
import requests
from datetime import datetime
from pathlib import Path

class RAGFlowAPIClient:
    """RAGFlow API client for document processing"""
    
    def __init__(self, api_base_url, api_key):
        self.api_base_url = api_base_url.rstrip('/')
        self.api_key = api_key
        self.headers = {
            'Authorization': f'Bearer {api_key}',
            'Content-Type': 'application/json'
        }
        self.session = requests.Session()
        self.session.headers.update({'Authorization': f'Bearer {api_key}'})
    
    def create_output_directory(self, base_output_path, pdf_filename):
        """Create timestamped output directory for saving step outputs"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        pdf_name = os.path.splitext(pdf_filename)[0]
        output_dir = os.path.join(base_output_path, f"{pdf_name}_api_{timestamp}")
        os.makedirs(output_dir, exist_ok=True)
        return output_dir
    
    def save_step_output(self, data, step_name, output_dir, filename_prefix=""):
        """Save output from each processing step"""
        try:
            if filename_prefix:
                filename = f"{filename_prefix}_{step_name}.json"
            else:
                filename = f"{step_name}.json"
            
            filepath = os.path.join(output_dir, filename)
            
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False, default=str)
            
            print(f"  💾 Saved {step_name} output to: {filepath}")
            return filepath
        except Exception as e:
            print(f"  ⚠️  Failed to save {step_name} output: {e}")
            return None
    
    def create_dataset(self, dataset_name, description=None, chunk_method="naive", chunk_token_num=512):
        """Step 1: Create a dataset"""
        print(f"1. Creating dataset: {dataset_name}")
        
        url = f"{self.api_base_url}/api/v1/datasets"
        
        payload = {
            "name": dataset_name,
            "description": description or f"Dataset for {dataset_name}",
            "chunk_method": chunk_method,
            "parser_config": {
                "chunk_token_num": chunk_token_num,
                "delimiter": "\n。；！？",
                "layout_recognize": "DeepDOC",
                "html4excel": False
            }
        }
        
        try:
            response = self.session.post(url, json=payload)
            response.raise_for_status()
            
            result = response.json()
            if result.get("code") == 0:
                dataset_info = result.get("data", {})
                dataset_id = dataset_info.get("id")
                print(f"  ✅ Dataset created successfully: {dataset_id}")
                return dataset_id, dataset_info
            else:
                print(f"  ❌ Failed to create dataset: {result.get('message', 'Unknown error')}")
                return None, None
                
        except requests.exceptions.RequestException as e:
            print(f"  ❌ Request failed: {e}")
            return None, None
    
    def upload_document(self, dataset_id, pdf_path):
        """Step 2: Upload PDF document to dataset"""
        print(f"2. Uploading document: {os.path.basename(pdf_path)}")
        
        if not os.path.exists(pdf_path):
            print(f"  ❌ PDF file does not exist: {pdf_path}")
            return None, None
        
        url = f"{self.api_base_url}/api/v1/datasets/{dataset_id}/documents"
        
        try:
            # Prepare file for upload
            with open(pdf_path, 'rb') as f:
                files = {'file': (os.path.basename(pdf_path), f, 'application/pdf')}
                
                # Remove Content-Type header for multipart upload
                headers = {'Authorization': f'Bearer {self.api_key}'}
                response = requests.post(url, files=files, headers=headers)
                response.raise_for_status()
            
            result = response.json()
            if result.get("code") == 0:
                documents = result.get("data", [])
                if documents:
                    document_info = documents[0]
                    document_id = document_info.get("id")
                    print(f"  ✅ Document uploaded successfully: {document_id}")
                    return document_id, document_info
                else:
                    print("  ❌ No document information returned")
                    return None, None
            else:
                print(f"  ❌ Failed to upload document: {result.get('message', 'Unknown error')}")
                return None, None
                
        except requests.exceptions.RequestException as e:
            print(f"  ❌ Upload request failed: {e}")
            return None, None
    
    def parse_documents(self, dataset_id, document_ids):
        """Step 3: Parse uploaded documents"""
        print(f"3. Parsing documents: {document_ids}")
        
        url = f"{self.api_base_url}/api/v1/datasets/{dataset_id}/chunks"
        
        payload = {
            "document_ids": document_ids if isinstance(document_ids, list) else [document_ids]
        }
        
        try:
            response = self.session.post(url, json=payload)
            response.raise_for_status()
            
            result = response.json()
            if result.get("code") == 0:
                print("  ✅ Document parsing initiated successfully")
                return True
            else:
                print(f"  ❌ Failed to initiate parsing: {result.get('message', 'Unknown error')}")
                return False
                
        except requests.exceptions.RequestException as e:
            print(f"  ❌ Parse request failed: {e}")
            return False
    
    def wait_for_parsing_completion(self, dataset_id, document_id, max_wait_time=300, check_interval=10):
        """Step 4: Wait for parsing to complete"""
        print(f"4. Waiting for parsing completion (max {max_wait_time}s)...")
        
        start_time = time.time()
        
        while time.time() - start_time < max_wait_time:
            # Check document status
            url = f"{self.api_base_url}/api/v1/datasets/{dataset_id}/documents"
            
            try:
                response = self.session.get(url)
                response.raise_for_status()
                
                result = response.json()
                if result.get("code") == 0:
                    documents = result.get("data", [])
                    
                    for doc in documents:
                        if doc.get("id") == document_id:
                            status = doc.get("status")
                            progress = doc.get("progress", 0)
                            
                            print(f"  📊 Parsing progress: {progress:.1%} (Status: {status})")
                            
                            if status == "1":  # Completed
                                print("  ✅ Document parsing completed!")
                                return True, doc
                            elif status == "2":  # Failed
                                print(f"  ❌ Document parsing failed: {doc.get('progress_msg', 'Unknown error')}")
                                return False, doc
                
                print(f"  ⏳ Still parsing... (elapsed: {int(time.time() - start_time)}s)")
                time.sleep(check_interval)
                
            except requests.exceptions.RequestException as e:
                print(f"  ⚠️  Status check failed: {e}")
                time.sleep(check_interval)
        
        print(f"  ⏰ Parsing timeout after {max_wait_time}s")
        return False, None
    
    def retrieve_chunks(self, dataset_id, document_ids=None, keywords="", page=1, page_size=100):
        """Step 5: Retrieve processed chunks"""
        print(f"5. Retrieving chunks from dataset: {dataset_id}")
        
        url = f"{self.api_base_url}/api/v1/datasets/{dataset_id}/chunks"
        
        params = {
            "page": page,
            "page_size": page_size
        }
        
        if document_ids:
            params["document_id"] = document_ids if isinstance(document_ids, str) else document_ids[0]
        
        if keywords:
            params["keywords"] = keywords
        
        try:
            response = self.session.get(url, params=params)
            response.raise_for_status()
            
            result = response.json()
            if result.get("code") == 0:
                chunks_data = result.get("data", {})
                chunks = chunks_data.get("chunks", [])
                total = chunks_data.get("total", 0)
                
                print(f"  ✅ Retrieved {len(chunks)} chunks (Total: {total})")
                return chunks, chunks_data
            else:
                print(f"  ❌ Failed to retrieve chunks: {result.get('message', 'Unknown error')}")
                return [], {}
                
        except requests.exceptions.RequestException as e:
            print(f"  ❌ Retrieve request failed: {e}")
            return [], {}
    
    def list_datasets(self):
        """List all datasets"""
        url = f"{self.api_base_url}/api/v1/datasets"
        
        try:
            response = self.session.get(url)
            response.raise_for_status()
            
            result = response.json()
            if result.get("code") == 0:
                return result.get("data", [])
            else:
                print(f"Failed to list datasets: {result.get('message', 'Unknown error')}")
                return []
                
        except requests.exceptions.RequestException as e:
            print(f"List datasets request failed: {e}")
            return []

def process_single_pdf_via_api(pdf_path, output_path, api_base_url, api_key, dataset_name=None):
    """Process a single PDF file using RAGFlow API"""
    print(f"🚀 Starting RAGFlow API Processing Pipeline")
    print("=" * 60)
    print(f"📄 Processing: {pdf_path}")
    print(f"📁 Output: {output_path}")
    print(f"🌐 API Server: {api_base_url}")

    if not os.path.exists(pdf_path):
        print(f"❌ PDF file does not exist: {pdf_path}")
        return []

    # Initialize API client
    client = RAGFlowAPIClient(api_base_url, api_key)

    # Create output directory
    filename = os.path.basename(pdf_path)
    pdf_output_dir = client.create_output_directory(output_path, filename)
    print(f"📁 Step outputs will be saved to: {pdf_output_dir}")

    # Generate dataset name if not provided
    if not dataset_name:
        pdf_name = os.path.splitext(filename)[0]
        dataset_name = f"API_Processing_{pdf_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

    try:
        # Step 1: Create dataset
        dataset_id, dataset_info = client.create_dataset(
            dataset_name=dataset_name,
            description=f"API processing of {filename}",
            chunk_method="naive",
            chunk_token_num=512
        )

        if not dataset_id:
            print("❌ Failed to create dataset, aborting...")
            return []

        # Save dataset creation output
        client.save_step_output(dataset_info, "01_dataset_creation", pdf_output_dir)

        # Step 2: Upload document
        document_id, document_info = client.upload_document(dataset_id, pdf_path)

        if not document_id:
            print("❌ Failed to upload document, aborting...")
            return []

        # Save document upload output
        client.save_step_output(document_info, "02_document_upload", pdf_output_dir)

        # Step 3: Parse documents
        parse_success = client.parse_documents(dataset_id, [document_id])

        if not parse_success:
            print("❌ Failed to initiate parsing, aborting...")
            return []

        # Save parsing initiation output
        client.save_step_output({"parsing_initiated": True, "document_id": document_id},
                               "03_parsing_initiation", pdf_output_dir)

        # Step 4: Wait for parsing completion
        parsing_completed, final_doc_info = client.wait_for_parsing_completion(
            dataset_id, document_id, max_wait_time=300, check_interval=10
        )

        if not parsing_completed:
            print("❌ Document parsing did not complete successfully")
            return []

        # Save parsing completion output
        client.save_step_output(final_doc_info, "04_parsing_completion", pdf_output_dir)

        # Step 5: Retrieve chunks
        chunks, chunks_data = client.retrieve_chunks(dataset_id, document_ids=[document_id])

        if not chunks:
            print("⚠️  No chunks retrieved, but processing may have completed")

        # Save chunks retrieval output
        client.save_step_output(chunks_data, "05_chunks_retrieval", pdf_output_dir)

        # Create readable chunks format
        readable_chunks = []
        for i, chunk in enumerate(chunks):
            readable_chunk = {
                "chunk_id": i + 1,
                "content": chunk.get("content", ""),
                "document_id": chunk.get("document_id", ""),
                "document_name": chunk.get("document_name", ""),
                "important_keywords": chunk.get("important_keywords", []),
                "positions": chunk.get("positions", [])
            }
            readable_chunks.append(readable_chunk)

        client.save_step_output(readable_chunks, "05_readable_chunks", pdf_output_dir)

        # Final summary
        summary = {
            "processing_method": "RAGFlow API",
            "api_server": api_base_url,
            "dataset_id": dataset_id,
            "dataset_name": dataset_name,
            "document_id": document_id,
            "document_name": filename,
            "total_chunks": len(chunks),
            "processing_timestamp": datetime.now().isoformat(),
            "output_directory": pdf_output_dir
        }

        client.save_step_output(summary, "06_processing_summary", pdf_output_dir)

        print(f"\n🎉 API processing completed!")
        print(f"📊 Total chunks processed: {len(chunks)}")
        print(f"📁 All outputs saved to: {pdf_output_dir}")
        print(f"🌐 Dataset ID: {dataset_id}")
        print(f"📄 Document ID: {document_id}")
        print(f"🔍 You can now view and search this document in RAGFlow UI!")
        print(f"   Dataset: {dataset_name}")

        return chunks

    except Exception as e:
        print(f"❌ Processing failed with error: {e}")
        return []

def main():
    """Main function"""
    # Configuration
    API_BASE_URL = "http://eoaagmld007.int.cgg.com"
    API_KEY = "YOUR_API_KEY_HERE"  # You need to replace this with your actual API key

    if len(sys.argv) == 1:
        # Default processing for your specific file
        pdf_path = r"/ml/shared/AI_Lab/g2module_docs/g2mod.pdf"
        output_path = r"/ml/shared/lazhang/data"

        print("🔑 Please set your RAGFlow API key in the script or pass it as environment variable")
        print("   You can get your API key from RAGFlow UI -> Settings -> API Keys")

        # Check for API key in environment
        api_key = os.getenv('RAGFLOW_API_KEY', API_KEY)
        if api_key == "YOUR_API_KEY_HERE":
            print("❌ Please set RAGFLOW_API_KEY environment variable or update the script")
            print("   Example: export RAGFLOW_API_KEY='your-actual-api-key'")
            sys.exit(1)

        chunks = process_single_pdf_via_api(pdf_path, output_path, API_BASE_URL, api_key)

    elif len(sys.argv) == 4:
        # Custom processing: python main_api.py <pdf_path> <output_path> <api_key>
        pdf_path = sys.argv[1]
        output_path = sys.argv[2]
        api_key = sys.argv[3]

        chunks = process_single_pdf_via_api(pdf_path, output_path, API_BASE_URL, api_key)

    else:
        print("Usage:")
        print("  python main_api.py                                    # Process default PDF (requires RAGFLOW_API_KEY env var)")
        print("  python main_api.py <pdf_path> <output_path> <api_key> # Process custom PDF")
        print()
        print("Examples:")
        print("  export RAGFLOW_API_KEY='your-api-key'")
        print("  python main_api.py")
        print("  python main_api.py ./doc.pdf ./output/ your-api-key")
        print()
        print("Get your API key from RAGFlow UI -> Settings -> API Keys")
        sys.exit(1)

    if chunks:
        print("\n📋 Sample chunk structure:")
        sample_chunk = chunks[0]
        for key, value in sample_chunk.items():
            if isinstance(value, str) and len(value) > 100:
                print(f"  {key}: {value[:100]}...")
            elif isinstance(value, list) and len(value) > 5:
                print(f"  {key}: [{len(value)} items]")
            else:
                print(f"  {key}: {value}")

if __name__ == "__main__":
    main()
